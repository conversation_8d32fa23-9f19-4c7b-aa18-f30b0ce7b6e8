#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GMGN代币交易量数据获取工具
获取SOL链上代币的24小时交易量数据
接口: /api/v1/mutil_window_token_info
"""

from curl_cffi import requests
import json
import sqlite3
import time
import sys
import random
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库配置
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "test_data_sol"

# GMGN API配置
API_URL = "https://gmgn.ai/api/v1/mutil_window_token_info"

# 代理配置
PRIMARY_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

BACKUP_PROXY = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

# 请求配置
MAX_RETRIES = 5
RETRY_INTERVAL = 3
REQUEST_TIMEOUT = 10
REQUEST_INTERVAL_MIN = 2
REQUEST_INTERVAL_MAX = 3

# 多线程配置
NUM_THREADS = 1

# 线程锁
PRINT_LOCK = threading.Lock()
DB_LOCK = threading.Lock()
PROXY_LOCK = threading.Lock()

# 当前代理
CURRENT_PROXY = PRIMARY_PROXY.copy()

# GMGN请求固定参数
COOKIES = {
    "_ga": "GA1.1.1787124478.1747114971",
    "cf_clearance": "uBGoNKbju0gJKlK8kYbfibSg6U5EzUtuftEIySzY3cs-1749797746-1.2.1.1-Sp4GRUUsOknzXfKNy5C8ajBOCitbdfHrc7yPkk8.CPFklkfwt8hjCRfPJk.DJ1vReQ_y61ILaAx3KaFZJIqgJtpOFycmM6M5Pk9aLmYwceQrqKVJl_hsAB7q.Oljo1cVKzat45qxXyZTBJAwLiujok_dOkeXhRppBwYVFxx7FtpjSIUIxFx45PQPDLGjiGq69sBp1jDlMv4yyxxs7jpcNq8gpV0Jx7IEhkNzrRT8DTO6iNxrMpbgdHu6fsc5gCXY_1I.GvUxonEwl3esoUeMyFkmPp7E_H6YcNL8VMOYpPlsumwwyqX_h4HbxIfWs3qkUU_mxPeZW_6Cdg04k278FcCX8mOxCzNUC_QOGsxzFAI",
    "_ga_0XM0LYXGC8": "GS2.1.s1749797734$o46$g1$t1749797748$j46$l0$h0"
}

PARAMS = {
    "device_id": "0582b587-c74b-4d09-9764-9a10c2cc8b87",
    "client_id": "gmgn_web_20250613-2203-ef1b00b",
    "from_app": "gmgn",
    "app_ver": "20250613-2203-ef1b00b",
    "tz_name": "Asia/Shanghai",
    "tz_offset": "28800",
    "app_lang": "zh-CN",
    "fp_did": "a0adc6758070914b5d3cd4679349eed1",
    "os": "web"
}


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with PRINT_LOCK:
        print(*args, **kwargs)


def get_headers(token_address):
    """生成GMGN请求头，动态设置referer"""
    headers = {
        "referer": f"https://gmgn.ai/sol/token/{token_address}",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    return headers


def get_token_addresses_from_db():
    """从数据库获取Token地址列表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(f'SELECT rowid, "code_address" FROM {DB_TABLE}')
        rows = cursor.fetchall()

        token_data = []
        for row_id, address in rows:
            if address and address.strip():
                token_data.append({
                    "row_id": row_id,
                    "token_address": address.strip()
                })

        thread_safe_print(f"从数据库获取到 {len(token_data)} 个Token地址")
        return token_data

    except sqlite3.Error as e:
        thread_safe_print(f"从数据库获取Token地址时出错: {e}")
        return []
    finally:
        conn.close()


def update_gmgn_volume_in_db(row_id, token_address, volume_24h):
    """更新数据库中的GMGN交易量数据"""
    with DB_LOCK:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            # 更新数据库
            cursor.execute(f'''
                UPDATE {DB_TABLE}
                SET "gmgn_volume" = ?
                WHERE rowid = ?
            ''', (volume_24h, row_id))

            if cursor.rowcount > 0:
                conn.commit()
                thread_safe_print(f"成功更新 {token_address} - 交易量: {volume_24h}")
                return True
            else:
                thread_safe_print(f"未找到记录 rowid: {row_id}")
                return False

        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库时出错: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()


def get_gmgn_volume_data(token_data, thread_id):
    """
    获取单个代币的GMGN交易量数据并立即写入数据库
    """
    global CURRENT_PROXY

    token_address = token_data["token_address"]
    row_id = token_data["row_id"]

    # 构造请求数据
    request_data = {
        "chain": "sol",
        "addresses": [token_address]
    }

    thread_safe_print(f"[线程-{thread_id}] 请求代币 {token_address} 的GMGN交易量数据...")

    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            # 获取当前代理
            with PROXY_LOCK:
                current_proxy = CURRENT_PROXY.copy()

            thread_safe_print(f"[线程-{thread_id}] {token_address} 尝试 {retry_count + 1}/{MAX_RETRIES}")

            # 生成动态请求头
            headers = get_headers(token_address)

            # 发送POST请求
            response = requests.post(
                API_URL,
                headers=headers,
                cookies=COOKIES,
                params=PARAMS,
                data=json.dumps(request_data, separators=(',', ':')),
                impersonate='chrome116',
                proxies=current_proxy,
                timeout=REQUEST_TIMEOUT
            )

            # 处理响应
            if response.status_code == 200:
                print(response.text)
                try:
                    result = response.json()

                    # 检查API返回状态
                    if result.get("code") == 0 and "data" in result and len(result["data"]) > 0:
                        token_info = result["data"][0]

                        # 提取volume_24h数据 - 修正数据路径
                        if "price" in token_info and "volume_24h" in token_info["price"]:
                            volume_24h = token_info["price"]["volume_24h"]

                            # 立即写入数据库
                            if update_gmgn_volume_in_db(row_id, token_address, volume_24h):
                                thread_safe_print(f"[线程-{thread_id}] {token_address} 数据已成功写入数据库")
                                return True
                            else:
                                thread_safe_print(f"[线程-{thread_id}] {token_address} 数据库写入失败")
                                return False
                        else:
                            thread_safe_print(f"[线程-{thread_id}] {token_address} 响应中未找到volume_24h字段")
                            return False

                    else:
                        error_msg = result.get("reason", "未知错误")
                        thread_safe_print(f"[线程-{thread_id}] {token_address} API错误: {error_msg}")

                except json.JSONDecodeError:
                    thread_safe_print(f"[线程-{thread_id}] {token_address} JSON解析失败: {response.text[:200]}...")
            else:
                thread_safe_print(
                    f"[线程-{thread_id}] {token_address} HTTP错误 {response.status_code}: {response.text[:200]}...")

            # 重试逻辑
            retry_count += 1
            if retry_count < MAX_RETRIES:
                thread_safe_print(f"[线程-{thread_id}] {token_address} 等待 {RETRY_INTERVAL} 秒后重试...")
                time.sleep(RETRY_INTERVAL)

        except requests.exceptions.RequestException as e:
            thread_safe_print(f"[线程-{thread_id}] {token_address} 请求异常: {e}")
            retry_count += 1
            if retry_count < MAX_RETRIES:
                time.sleep(RETRY_INTERVAL)

        except Exception as e:
            thread_safe_print(f"[线程-{thread_id}] {token_address} 未知错误: {e}")
            retry_count += 1
            if retry_count < MAX_RETRIES:
                time.sleep(RETRY_INTERVAL)

    thread_safe_print(f"[线程-{thread_id}] {token_address} 已达到最大重试次数，获取失败")
    return False


def switch_to_backup_proxy():
    """切换到备用代理"""
    global CURRENT_PROXY
    with PROXY_LOCK:
        CURRENT_PROXY = BACKUP_PROXY.copy()
        thread_safe_print("已切换到备用代理")


def worker_thread(token_list, thread_id):
    """工作线程函数，处理多个代币"""
    thread_safe_print(f"[线程-{thread_id}] 开始处理 {len(token_list)} 个代币")

    success_count = 0

    for i, token_data in enumerate(token_list):
        thread_safe_print(f"[线程-{thread_id}] 处理第 {i + 1}/{len(token_list)} 个代币: {token_data['token_address']}")

        # 获取GMGN交易量数据并立即写入数据库
        if get_gmgn_volume_data(token_data, thread_id):
            success_count += 1

        # 请求间隔
        if i < len(token_list) - 1:
            delay = random.uniform(REQUEST_INTERVAL_MIN, REQUEST_INTERVAL_MAX)
            thread_safe_print(f"[线程-{thread_id}] 等待 {delay:.2f} 秒...")
            time.sleep(delay)

    thread_safe_print(f"[线程-{thread_id}] 完成处理，成功: {success_count}/{len(token_list)}")
    return success_count


def process_all_tokens():
    """处理所有代币数据"""
    # 获取所有token地址
    all_tokens = get_token_addresses_from_db()

    if not all_tokens:
        thread_safe_print("未找到任何Token地址")
        return 0, 0

    total_count = len(all_tokens)
    thread_safe_print(f"总共需要处理 {total_count} 个代币，使用 {NUM_THREADS} 个线程")

    # 将代币分配给各个线程
    tokens_per_thread = total_count // NUM_THREADS
    remainder = total_count % NUM_THREADS

    thread_tokens = []
    start_idx = 0

    for i in range(NUM_THREADS):
        token_count = tokens_per_thread + (1 if i < remainder else 0)
        end_idx = start_idx + token_count
        thread_tokens.append(all_tokens[start_idx:end_idx])
        thread_safe_print(f"线程-{i + 1} 分配到 {len(thread_tokens[-1])} 个代币")
        start_idx = end_idx

    # 使用线程池执行
    success_counts = []
    with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
        futures = [executor.submit(worker_thread, thread_tokens[i], i + 1) for i in range(NUM_THREADS)]

        for future in futures:
            success_counts.append(future.result())

    total_success = sum(success_counts)
    return total_count, total_success


def main():
    """主函数"""
    thread_safe_print("=" * 80)
    thread_safe_print("GMGN代币交易量数据获取工具")
    thread_safe_print("获取SOL链上代币的24小时交易量数据")
    thread_safe_print("=" * 80)
    thread_safe_print(f"API URL: {API_URL}")
    thread_safe_print(f"数据库: {DB_PATH}")
    thread_safe_print(f"数据表: {DB_TABLE}")
    thread_safe_print(f"线程数: {NUM_THREADS}")
    thread_safe_print("=" * 80)

    start_time = time.time()

    try:
        total_count, success_count = process_all_tokens()

        thread_safe_print("\n" + "=" * 80)
        thread_safe_print("处理完成！")
        thread_safe_print(f"总计处理: {total_count} 个代币")
        thread_safe_print(f"成功更新: {success_count} 个代币")
        thread_safe_print(f"成功率: {success_count / total_count * 100:.2f}%" if total_count > 0 else "成功率: 0%")

    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        thread_safe_print(f"程序运行错误: {e}")
        import traceback
        thread_safe_print(traceback.format_exc())

    end_time = time.time()
    thread_safe_print(f"总耗时: {end_time - start_time:.2f} 秒")
    thread_safe_print("=" * 80)


if __name__ == "__main__":
    main()