#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HA代币数据获取工具
获取SOL链上代币的涨跌幅差异和交易量数据
接口: /api/v1/dex/market/stats
同时写入ha_change(涨跌幅)和ha_volume(交易量)字段
"""

import requests
import json
import sqlite3
import time
import sys
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

if sys.stdout.encoding != 'utf-8':
    try:
        sys.stdout.reconfigure(encoding='utf-8')
    except AttributeError:
        pass

# 数据库配置
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "test_data_sol_2"

# HA API配置
# API_URL = "https://www.valuescan.ai/api/v1/dex/market/stats"
# API_URL = "http://192.168.224.29/api/v1/dex/market/stats"
API_URL = "http://192.168.224.29/api/v1/dex/market/coin-stats"

# 代理配置
PRIMARY_PROXY = {
    "http": "http://127.0.0.1:33210",
    "https": "http://127.0.0.1:33210"
}

BACKUP_PROXY = {
    "http": "socks5://192.168.224.75:30889",
    "https": "socks5://192.168.224.75:30889"
}

# 请求配置
MAX_RETRIES = 5
RETRY_INTERVAL = 1
REQUEST_TIMEOUT = 10
REQUEST_INTERVAL = 2  # 请求间隔秒数

# 线程配置
THREAD_COUNT = 1  # 线程数

# 线程锁
PRINT_LOCK = threading.Lock()
DB_LOCK = threading.Lock()
PROXY_LOCK = threading.Lock()

# 当前代理
CURRENT_PROXY = PRIMARY_PROXY.copy()


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with PRINT_LOCK:
        print(*args, **kwargs)


def get_headers():
    """生成HA请求头"""
    headers = {
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    return headers


def get_token_addresses_from_db():
    """从数据库获取Token地址列表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        cursor.execute(f'SELECT rowid, "code_address" FROM {DB_TABLE}')
        rows = cursor.fetchall()

        token_data = []
        for row_id, address in rows:
            if address and address.strip():
                token_data.append({
                    "row_id": row_id,
                    "token_address": address.strip()
                })

        thread_safe_print(f"从数据库获取到 {len(token_data)} 个Token地址")
        return token_data

    except sqlite3.Error as e:
        thread_safe_print(f"从数据库获取Token地址时出错: {e}")
        return []
    finally:
        conn.close()


def update_ha_data_in_db(row_id, token_address, price_change, vol_usd):
    """更新数据库中的HA涨跌幅和交易量数据"""
    with DB_LOCK:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        try:
            # 更新数据库 - 同时更新ha_change和ha_volume字段
            cursor.execute(f'''
                UPDATE {DB_TABLE}
                SET "ha_change" = ?, "ha_volume" = ?
                WHERE rowid = ?
            ''', (price_change, vol_usd, row_id))

            if cursor.rowcount > 0:
                conn.commit()
                thread_safe_print(f"成功更新 {token_address} - 涨跌幅: {price_change}, 交易量: {vol_usd}")
                return True
            else:
                thread_safe_print(f"未找到记录 rowid: {row_id}")
                return False

        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库时出错: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()


def get_single_ha_data(token_data, request_data):
    """获取单个代币的HA数据"""
    row_id = token_data["row_id"]
    token_address = token_data["token_address"]

    thread_safe_print(f"请求代币 {token_address} 的HA数据...")

    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            # 生成请求头
            headers = get_headers()

            # 发送POST请求
            response = requests.post(
                API_URL,
                headers=headers,
                json=request_data,
                proxies=PRIMARY_PROXY,
                timeout=REQUEST_TIMEOUT
            )

            # 处理响应
            if response.status_code == 200:
                try:
                    result = response.json()

                    # 检查API返回状态
                    if result.get("code") == 200 and "data" in result:
                        stats_data = result["data"]

                        if len(stats_data) > 0:
                            stats = stats_data[0]
                            price_change = stats.get("priceChange")
                            volume = stats.get("volUsd")

                            if price_change is not None:
                                # 立即写入数据库 - 同时写入价格变化和交易量
                                if update_ha_data_in_db(row_id, token_address, price_change, volume):
                                    thread_safe_print(f"{token_address} 数据已成功写入数据库 - 涨跌幅: {price_change}, 交易量: {volume}")
                                    return True
                                else:
                                    thread_safe_print(f"{token_address} 写入数据库失败")
                            else:
                                thread_safe_print(f"{token_address} 响应数据缺少价格变化信息")
                        else:
                            thread_safe_print(f"{token_address} 响应数据为空")
                    else:
                        error_msg = result.get("msg", "未知错误")
                        thread_safe_print(f"API错误: {error_msg}")

                except json.JSONDecodeError:
                    thread_safe_print(f"JSON解析失败: {response.text[:200]}...")
            else:
                thread_safe_print(f"HTTP错误 {response.status_code}: {response.text[:200]}...")

            # 重试逻辑
            retry_count += 1
            if retry_count < MAX_RETRIES:
                thread_safe_print(f"等待 {RETRY_INTERVAL} 秒后重试...")
                time.sleep(RETRY_INTERVAL)

        except requests.exceptions.RequestException as e:
            thread_safe_print(f"请求异常: {e}")
            retry_count += 1
            if retry_count < MAX_RETRIES:
                time.sleep(RETRY_INTERVAL)

        except Exception as e:
            thread_safe_print(f"未知错误: {e}")
            retry_count += 1
            if retry_count < MAX_RETRIES:
                time.sleep(RETRY_INTERVAL)

    thread_safe_print(f"{token_address} 已达到最大重试次数，获取失败")
    return False


def process_all_tokens():
    """处理所有代币数据"""
    # 获取所有token地址
    all_tokens = get_token_addresses_from_db()

    if not all_tokens:
        thread_safe_print("未找到任何Token地址")
        return 0, 0

    total_count = len(all_tokens)
    thread_safe_print(f"总共需要处理 {total_count} 个代币")

    success_count = 0

    # 逐个处理代币
    for i, token_data in enumerate(all_tokens):
        row_id = token_data["row_id"]
        token_address = token_data["token_address"]

        thread_safe_print(f"处理第 {i + 1}/{total_count} 个代币: {token_address}")

        # 构造请求数据
        coin_keys = [{
            "chainName": "SOL",
            "tokenContractAddress": token_address
        }]

        request_data = {
            "bar": "24h",
            "coinKeys": coin_keys
        }

        # 获取HA数据
        if get_single_ha_data(token_data, request_data):
            success_count += 1
            thread_safe_print(f"当前成功: {success_count}/{i + 1}")

        # 请求间隔
        if i < total_count - 1:
            thread_safe_print(f"等待 {REQUEST_INTERVAL} 秒...")
            time.sleep(REQUEST_INTERVAL)

    return total_count, success_count


def main():
    """主函数"""
    thread_safe_print("=" * 80)
    thread_safe_print("HA代币数据获取工具")
    thread_safe_print("获取SOL链上代币的涨跌幅差异和交易量数据")
    thread_safe_print("同时写入ha_change(涨跌幅)和ha_volume(交易量)字段")
    thread_safe_print("=" * 80)
    thread_safe_print(f"API URL: {API_URL}")
    thread_safe_print(f"数据库: {DB_PATH}")
    thread_safe_print(f"数据表: {DB_TABLE}")
    thread_safe_print(f"线程数: {THREAD_COUNT}")
    thread_safe_print(f"请求间隔: {REQUEST_INTERVAL} 秒")
    thread_safe_print("=" * 80)

    start_time = time.time()

    try:
        total_count, success_count = process_all_tokens()

        thread_safe_print("\n" + "=" * 80)
        thread_safe_print("处理完成！")
        thread_safe_print(f"总计处理: {total_count} 个代币")
        thread_safe_print(f"成功更新: {success_count} 个代币")
        thread_safe_print(f"成功率: {success_count / total_count * 100:.2f}%" if total_count > 0 else "成功率: 0%")

    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        thread_safe_print(f"程序运行错误: {e}")
        import traceback
        thread_safe_print(traceback.format_exc())

    end_time = time.time()
    thread_safe_print(f"总耗时: {end_time - start_time:.2f} 秒")
    thread_safe_print("=" * 80)


if __name__ == "__main__":
    main()
