#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取Solana链上代币的最新价格数据并更新到数据库的"data OKX"字段
使用OKX官方DEX API获取Solana代币的历史综合币价
"""

import requests
import json
import sqlite3
import time
import datetime
import hmac
import base64
import hashlib
import threading
from concurrent.futures import ThreadPoolExecutor

# 数据库路径
DB_PATH = r"C:\Users\<USER>\AppData\Roaming\DBeaverData\workspace6\.metadata\sample-database-sqlite-1\Chinook.db"
DB_TABLE = "test_data_bsc"  # 修改为正确的表名

# OKX DEX API配置
OKX_API_HOST = "https://web3.okx.com"
OKX_HISTORICAL_PRICE_API = "/api/v5/dex/index/historical-price"  # 历史价格API

# Solana链ID
SOLANA_CHAIN_ID = "56"

# OKX API认证参数
API_KEY = "d87d0a5f-a4df-4209-a3b7-573dad862d25"
API_SECRET = "8807594F0F5B6A15F2B223638B8537D0"
API_PASSPHRASE = "Dh112211!"

# 批量处理大小
BATCH_SIZE = 1  # 单个处理，降低API请求频率

# 默认时间戳 (2025-05-28 17:25:27 的时间戳，毫秒)
DEFAULT_TIMESTAMP = int(datetime.datetime(2025, 6, 12, 17, 11, 25).timestamp() * 1000)

# 线程数量
NUM_THREADS = 1  # 修改为10个线程

# 重试配置
MAX_RETRIES = 5  # 增加重试次数
RETRY_INTERVAL = 2  # 秒

# 线程锁，用于打印日志
PRINT_LOCK = threading.Lock()

# 数据库锁，用于多线程环境下的数据库访问
DB_LOCK = threading.Lock()


def thread_safe_print(*args, **kwargs):
    """线程安全的打印函数"""
    with PRINT_LOCK:
        print(*args, **kwargs)


def get_token_count():
    """获取需要处理的Token总数"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # 计算非空Token Address的总数
        cursor.execute(f'SELECT COUNT(*) FROM {DB_TABLE} WHERE "Token Address" IS NOT NULL')
        count = cursor.fetchone()[0]
    except sqlite3.Error as e:
        thread_safe_print(f"获取Token总数时出错: {e}")
        count = 0
    finally:
        conn.close()

    return count


def get_all_tokens():
    """获取所有需要处理的token数据"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # 获取所有非空Token Address记录
        cursor.execute(f'SELECT rowid, "Token Address", "HA Time" FROM {DB_TABLE} WHERE "Token Address" IS NOT NULL')
        rows = cursor.fetchall()

        token_data_list = []
        for row in rows:
            token_data_list.append({
                "row_id": row[0],
                "token_address": row[1],
                "ha_time": row[2]
            })

        return token_data_list
    except sqlite3.Error as e:
        thread_safe_print(f"获取所有Token数据时出错: {e}")
        return []
    finally:
        conn.close()


def convert_time_to_timestamp(time_str):
    """将标准时间字符串转换为毫秒时间戳"""
    if not time_str:
        # 如果时间为空，返回默认时间戳
        return DEFAULT_TIMESTAMP

    try:
        # 尝试解析时间字符串
        dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        # 转换为毫秒时间戳
        return int(dt.timestamp() * 1000)
    except (ValueError, TypeError) as e:
        thread_safe_print(f"时间转换错误: {e}, 使用默认时间戳")
        return DEFAULT_TIMESTAMP


def generate_signature(timestamp, method, request_path, params=None, body=None, secret_key=None):
    """生成OKX API签名"""
    # 构建签名字符串
    if params:
        request_path = request_path + '?' + '&'.join([f"{k}={v}" for k, v in params.items()])

    if body and isinstance(body, (dict, list)):
        body = json.dumps(body)

    message = timestamp + method + request_path + (body or '')

    mac = hmac.new(
        bytes(secret_key, encoding='utf8'),
        bytes(message, encoding='utf-8'),
        digestmod=hashlib.sha256
    )

    signature = base64.b64encode(mac.digest()).decode('utf-8')
    return signature


def clean_token_address(address):
    """
    清理和格式化代币地址

    Args:
        address: 原始代币地址

    Returns:
        str: 格式化后的地址
    """
    # 注意：现在不再去掉"pump"后缀，使用原始地址
    # 仅移除可能的空格和特殊字符
    address = address.strip()

    return address


def update_price_in_db(row_id, price, timestamp_str):
    """
    将单个代币的价格数据立即更新到数据库

    Args:
        row_id: 数据库行ID
        price: 价格数据
        timestamp_str: 时间戳字符串

    Returns:
        bool: 更新是否成功
    """
    # 使用数据库锁确保线程安全
    with DB_LOCK:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        success = False

        try:
            # 时间字符串格式转换
            if timestamp_str:
                try:
                    # 如果是毫秒时间戳
                    timestamp_int = int(timestamp_str)
                    timestamp = timestamp_int / 1000
                    time_obj = datetime.datetime.fromtimestamp(timestamp)
                    time_str = time_obj.strftime('%Y-%m-%d %H:%M:%S')
                    thread_safe_print(f"时间戳 {timestamp_str} 转换为标准时间: {time_str}")
                except (ValueError, TypeError) as e:
                    # 如果不是数字格式，直接使用原字符串
                    time_str = timestamp_str
                    thread_safe_print(f"时间戳转换失败: {e}, 使用原始时间字符串: {time_str}")
            else:
                time_str = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                thread_safe_print("没有时间戳数据，使用当前时间")

            # 更新数据库
            cursor.execute(
                f'''
                UPDATE {DB_TABLE}
                SET "okx_price" = ?,
                    "OKX Time" = ?
                WHERE rowid = ?
                ''',
                (price, time_str, row_id)
            )

            conn.commit()

            if cursor.rowcount > 0:
                thread_safe_print(f"成功更新 rowid {row_id} 的价格: {price}, 时间: {time_str}")
                success = True
            else:
                thread_safe_print(f"未找到rowid {row_id}")

        except sqlite3.Error as e:
            thread_safe_print(f"更新数据库时出错: {e}")
            conn.rollback()

        finally:
            conn.close()

        return success


def fetch_historical_price(token_data, thread_id):
    """获取单个代币的历史价格"""
    row_id = token_data["row_id"]
    token_address = token_data["token_address"]
    ha_time = token_data["ha_time"]

    # 清理代币地址
    clean_address = clean_token_address(token_address)
    if not clean_address:
        thread_safe_print(f"[线程-{thread_id}] 地址 {token_address} 格式化后为空，跳过")
        return False

    # 将HA Time转换为时间戳
    begin_timestamp = convert_time_to_timestamp(ha_time)

    # 构建请求参数
    params = {
        "chainIndex": SOLANA_CHAIN_ID,
        "tokenContractAddress": clean_address,
        "begin": str(begin_timestamp),
        "limit": "1",  # 只需要最近的一条数据
        "period": "1d"  # 按天获取数据
    }

    # 构建请求URL和请求头
    url = f"{OKX_API_HOST}{OKX_HISTORICAL_PRICE_API}"

    retry_count = 0
    while retry_count < MAX_RETRIES:
        try:
            # 生成时间戳和签名
            timestamp = datetime.datetime.utcnow().isoformat("T", "milliseconds") + "Z"
            signature = generate_signature(timestamp, "GET", OKX_HISTORICAL_PRICE_API, params, None, API_SECRET)

            headers = {
                "Content-Type": "application/json",
                "OK-ACCESS-KEY": API_KEY,
                "OK-ACCESS-SIGN": signature,
                "OK-ACCESS-TIMESTAMP": timestamp,
                "OK-ACCESS-PASSPHRASE": API_PASSPHRASE
            }

            # 构建完整URL（带参数）
            full_url = url + "?" + "&".join([f"{k}={v}" for k, v in params.items()])

            # 打印请求信息
            thread_safe_print(
                f"\n[线程-{thread_id}] 请求代币 {token_address} 的历史价格... (尝试 {retry_count + 1}/{MAX_RETRIES})")
            thread_safe_print(f"[线程-{thread_id}] 请求URL: {full_url}")
            thread_safe_print(f"[线程-{thread_id}] 使用时间戳: {begin_timestamp} (来自HA Time: {ha_time})")

            # 发送GET请求
            response = requests.get(full_url, headers=headers, timeout=30)

            thread_safe_print(f"[线程-{thread_id}] 响应状态码: {response.status_code}")

            if response.status_code != 200:
                thread_safe_print(f"[线程-{thread_id}] 请求失败: {response.text}")
                retry_count += 1
                if retry_count < MAX_RETRIES:
                    thread_safe_print(f"[线程-{thread_id}] 等待 {RETRY_INTERVAL} 秒后重试...")
                    time.sleep(RETRY_INTERVAL)
                continue

            # 解析响应
            try:
                data = response.json()

                if data.get("code") == "0" and "data" in data and data["data"]:
                    prices_data = data["data"][0].get("prices", [])

                    if prices_data and len(prices_data) > 0:
                        # 获取第一条价格记录
                        price_record = prices_data[0]
                        price = price_record.get("price")
                        time_ms = price_record.get("time")

                        if price:
                            # 更新到数据库
                            return update_price_in_db(row_id, price, time_ms)
                        else:
                            thread_safe_print(f"[线程-{thread_id}] 获取到的价格数据为空")
                    else:
                        thread_safe_print(f"[线程-{thread_id}] 没有找到历史价格数据")
                else:
                    error_msg = data.get("msg", "未知错误")
                    thread_safe_print(f"[线程-{thread_id}] API请求失败: {error_msg}")

                # 即使没有价格数据，但请求成功，不需要重试
                break

            except json.JSONDecodeError:
                thread_safe_print(f"[线程-{thread_id}] JSON解析错误，响应内容: {response.text}")
                retry_count += 1
                if retry_count < MAX_RETRIES:
                    thread_safe_print(f"[线程-{thread_id}] 等待 {RETRY_INTERVAL} 秒后重试...")
                    time.sleep(RETRY_INTERVAL)

        except requests.exceptions.RequestException as e:
            thread_safe_print(f"[线程-{thread_id}] 请求异常: {e}")
            retry_count += 1
            if retry_count < MAX_RETRIES:
                thread_safe_print(f"[线程-{thread_id}] 等待 {RETRY_INTERVAL} 秒后重试...")
                time.sleep(RETRY_INTERVAL)

        except Exception as e:
            thread_safe_print(f"[线程-{thread_id}] 请求代币 {token_address} 出错: {e}")
            import traceback
            thread_safe_print(traceback.format_exc())
            retry_count += 1
            if retry_count < MAX_RETRIES:
                thread_safe_print(f"[线程-{thread_id}] 等待 {RETRY_INTERVAL} 秒后重试...")
                time.sleep(RETRY_INTERVAL)

    thread_safe_print(f"[线程-{thread_id}] 已达到最大重试次数，放弃获取此代币的数据")
    return False


def worker_thread(token_data_batch, thread_id):
    """工作线程函数，处理一批token数据"""
    thread_safe_print(f"[线程-{thread_id}] 启动，负责处理 {len(token_data_batch)} 个代币")

    success_count = 0
    processed_count = 0
    total_count = len(token_data_batch)

    for i, token_data in enumerate(token_data_batch):
        processed_count += 1
        thread_safe_print(f"\n[线程-{thread_id}] 处理第 {i + 1}/{total_count} 个代币: {token_data['token_address']}")

        # 获取历史价格并更新数据库
        if fetch_historical_price(token_data, thread_id):
            success_count += 1
            thread_safe_print(
                f"[线程-{thread_id}] 进度: {processed_count}/{total_count} ({processed_count / total_count * 100:.2f}%)")

        # 每次请求后等待1-2秒，避免请求过于频繁，在并发环境下可以适当减少等待时间
        if i < total_count - 1:  # 最后一个不需要延迟
            delay = 1.5  # 并发环境下减少延迟
            thread_safe_print(f"[线程-{thread_id}] 等待 {delay} 秒...")
            time.sleep(delay)

    thread_safe_print(f"[线程-{thread_id}] 完成! 成功处理 {success_count}/{total_count} 个代币")
    return success_count


def process_token_prices_with_threads():
    """使用多线程处理所有token数据"""
    # 获取所有token数据
    all_tokens = get_all_tokens()
    total_count = len(all_tokens)

    if not all_tokens:
        thread_safe_print("未找到任何Token数据，请检查数据库")
        return 0, 0

    thread_safe_print(f"总共有 {total_count} 个代币需要处理，将使用 {NUM_THREADS} 个线程并行处理")

    # 将token数据平均分配给各个线程
    tokens_per_thread = total_count // NUM_THREADS
    remainder = total_count % NUM_THREADS

    token_batches = []
    start_idx = 0

    for i in range(NUM_THREADS):
        # 计算每个线程分配的token数量，考虑余数分配
        batch_size = tokens_per_thread + (1 if i < remainder else 0)
        end_idx = start_idx + batch_size

        # 分配token批次
        token_batches.append(all_tokens[start_idx:end_idx])
        thread_safe_print(f"线程-{i + 1} 将处理 {len(token_batches[-1])} 个代币，索引范围: {start_idx}-{end_idx - 1}")

        start_idx = end_idx

    # 使用线程池执行任务
    success_counts = []
    with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
        # 提交任务并收集Future对象
        futures = [executor.submit(worker_thread, token_batches[i], i + 1) for i in range(NUM_THREADS)]

        # 等待所有任务完成并收集结果
        for future in futures:
            success_counts.append(future.result())

    # 计算总成功数
    total_success = sum(success_counts)

    return total_count, total_success


def main():
    """主函数"""
    thread_safe_print("=" * 60)
    thread_safe_print("OKX Token历史价格获取工具 (多线程版)")
    thread_safe_print("=" * 60)
    thread_safe_print("数据库路径:", DB_PATH)
    thread_safe_print("数据库表名:", DB_TABLE)
    thread_safe_print("OKX API主机:", OKX_API_HOST)
    thread_safe_print("使用API:", OKX_HISTORICAL_PRICE_API)
    thread_safe_print("Solana链ID:", SOLANA_CHAIN_ID)
    thread_safe_print("线程数量:", NUM_THREADS)
    thread_safe_print("最大重试次数:", MAX_RETRIES)
    thread_safe_print("默认时间戳:", DEFAULT_TIMESTAMP,
                      f"({datetime.datetime.fromtimestamp(DEFAULT_TIMESTAMP / 1000).strftime('%Y-%m-%d %H:%M:%S')})")
    thread_safe_print("=" * 60)

    # 获取价格并实时更新数据库
    thread_safe_print("\n获取历史价格并实时更新数据库...")
    total_count, success_count = process_token_prices_with_threads()

    # 输出结果
    thread_safe_print("\n更新完成!")
    thread_safe_print(f"总计: {total_count} 个Token")
    thread_safe_print(f"成功更新数据库: {success_count} 个")


if __name__ == "__main__":
    thread_safe_print("开始获取Solana链上代币的历史价格数据...")
    start_time = time.time()

    try:
        main()
    except KeyboardInterrupt:
        thread_safe_print("\n程序被用户中断")
    except Exception as e:
        import traceback

        thread_safe_print(f"程序运行出错: {e}")
        thread_safe_print(traceback.format_exc())

    end_time = time.time()
    duration = end_time - start_time
    thread_safe_print(f"程序运行耗时: {duration:.2f} 秒")
    thread_safe_print("程序运行结束")